#include <iostream>
#include <windows.h>
#include "libaryVersionInfo.h"

int main() {
    std::cout << "Testing Windows DLL Version Info..." << std::endl;

    // 模拟ldfcr初始化
    std::cout << "Simulating ldfcr initialization..." << std::endl;
    Sleep(100); // 等待库加载

    libaryVersionInfo versionInfo;

    std::cout << "\n=== Baseline Libraries (Program Startup) ===\n";
    versionInfo.GetVersionInfoByOrder();
    versionInfo.PrintVersionInfo();

    std::cout << "\n=== Testing individual DLL info ===\n";
    // 测试单个DLL信息获取
    libaryVersionInfo testInfo;

    // 测试一些常见的系统DLL
    const char* testDlls[] = {
        "kernel32.dll",
        "user32.dll",
        "ntdll.dll"
    };

    for (int i = 0; i < 3; i++) {
        std::string dllPath = std::string("C:\\Windows\\System32\\") + testDlls[i];
        std::string info = testInfo.testGetDllVersionInfo(dllPath.c_str());
        if (!info.empty()) {
            std::cout << info << std::endl;
        }
    }

    std::cout << "\n=== Baseline Libraries (Program End) ===\n";
    versionInfo.PrintVersionInfo();

    return 0;
}
