@echo off
echo 快速编译测试...

echo 1. 测试libaryVersionInfo.h编译
cl /EHsc /c /I. test_compile.cpp
if %ERRORLEVEL% NEQ 0 (
    echo libaryVersionInfo.h编译失败！
    pause
    exit /b 1
)

echo 2. 测试轻量级演示程序编译
cl /EHsc lightweight_demo.cpp
if %ERRORLEVEL% NEQ 0 (
    echo 轻量级演示程序编译失败！
    pause
    exit /b 1
)

echo 3. 运行轻量级演示
lightweight_demo.exe

echo.
echo 所有测试通过！
echo.
echo 如果main.cpp仍有编译问题，请：
echo 1. 清理项目 (Clean Solution)
echo 2. 重新生成 (Rebuild All)
echo 3. 检查项目设置中的包含路径

pause
