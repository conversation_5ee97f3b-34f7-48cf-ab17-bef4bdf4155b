/*
 * 轻量级Windows DLL版本信息演示
 * 使用最少的Windows API和C++标准库
 */

#include <iostream>  // for cout, endl
#include <string>    // for string
#include <vector>    // for vector
#include <ctime>     // for localtime, struct tm
#include <cstdio>    // for snprintf

#ifdef _WIN32
#include <windows.h> // for FindFirstFile, GetModuleFileName
#include <io.h>      // for _access
#include <sys/stat.h> // for _stat
#endif

using namespace std;

// 轻量级文件时间获取
string getFileTimeString(const char* filePath) {
    struct _stat fileStat;
    if (_stat(filePath, &fileStat) == 0) {
        struct tm* timeinfo = localtime(&fileStat.st_mtime);
        if (timeinfo) {
            char timeStr[64];
            snprintf(timeStr, sizeof(timeStr), "%04d-%02d-%02d %02d:%02d:%02d",
                timeinfo->tm_year + 1900, timeinfo->tm_mon + 1, timeinfo->tm_mday,
                timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec);
            return string(timeStr);
        }
    }
    return "Unknown";
}

// 基于文件名生成一致的GUID
string generateConsistentGuid(const char* fileName) {
    unsigned int hash = 0;
    const char* p = fileName;
    while (*p) {
        // 简单的转小写：如果是大写字母，转为小写
        char c = (*p >= 'A' && *p <= 'Z') ? (*p + 32) : *p;
        hash = hash * 31 + c;
        p++;
    }
    
    char guid[64];
    snprintf(guid, sizeof(guid), "{%08x-%04x-%04x-%04x-%08x%04x}",
        hash, 
        (hash >> 16) & 0xFFFF, 
        (hash >> 8) & 0xFFFF,
        hash & 0xFFFF, 
        hash ^ 0x12345678, 
        hash & 0xFFFF);
    
    return string(guid);
}

// 轻量级版本信息获取
string getLightweightDllInfo(const char* dllPath) {
    // 检查文件是否存在
    if (_access(dllPath, 0) != 0) {
        return "";
    }

    // 提取文件名
    const char* lastSlash = strrchr(dllPath, '\\');
    const char* lastSlash2 = strrchr(dllPath, '/');
    const char* baseName = dllPath;
    if (lastSlash && (!lastSlash2 || lastSlash > lastSlash2)) {
        baseName = lastSlash + 1;
    } else if (lastSlash2) {
        baseName = lastSlash2 + 1;
    }

    // 获取文件时间
    string timeStr = getFileTimeString(dllPath);
    
    // 生成一致的GUID
    string guidStr = generateConsistentGuid(baseName);
    
    // 简化的版本获取 - 使用文件修改时间作为版本标识
    string versionStr = "1.0.0.0"; // 默认版本
    
    // 尝试从文件修改时间生成版本号
    struct _stat fileStat;
    if (_stat(dllPath, &fileStat) == 0) {
        struct tm* timeinfo = localtime(&fileStat.st_mtime);
        if (timeinfo) {
            char version[32];
            snprintf(version, sizeof(version), "%d.%d.%d.%d", 
                max(1, timeinfo->tm_year + 1900 - 2020),  // 年份偏移，最小为1
                timeinfo->tm_mon + 1,                     // 月份
                timeinfo->tm_mday,                        // 日期
                timeinfo->tm_hour                         // 小时
            );
            versionStr = version;
        }
    }

    // 格式化输出
    char result[512];
    snprintf(result, sizeof(result), "%-21s %-13s %-19s %s", 
        baseName, versionStr.c_str(), timeStr.c_str(), guidStr.c_str());

    return string(result);
}

// 扫描目录查找DLL
void scanDirectoryForDlls(const string& dirPath, vector<string>& dllPaths) {
    string searchPath = dirPath;
    if (searchPath.back() != '\\' && searchPath.back() != '/') {
        searchPath += "\\";
    }
    searchPath += "*.dll";
    
    WIN32_FIND_DATAA findData;
    HANDLE hFind = FindFirstFileA(searchPath.c_str(), &findData);
    
    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            if (!(findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)) {
                string fullPath = dirPath;
                if (fullPath.back() != '\\' && fullPath.back() != '/') {
                    fullPath += "\\";
                }
                fullPath += findData.cFileName;
                dllPaths.push_back(fullPath);
            }
        } while (FindNextFileA(hFind, &findData));
        
        FindClose(hFind);
    }
}

int main() {
    cout << "=== 轻量级Windows DLL版本信息演示 ===" << endl;
    cout << "文件名                版本号        时间                GUID" << endl;
    cout << "================================================================" << endl;
    
    // 获取当前可执行文件目录
    char exePath[MAX_PATH];
    string currentDir = ".\\";
    if (GetModuleFileNameA(NULL, exePath, MAX_PATH)) {
        char* lastSlash = strrchr(exePath, '\\');
        if (lastSlash) {
            *lastSlash = '\0';
            currentDir = string(exePath) + "\\";
        }
    }
    
    // 扫描当前目录的DLL文件
    vector<string> dllPaths;
    scanDirectoryForDlls(currentDir, dllPaths);
    
    if (dllPaths.empty()) {
        cout << "当前目录未找到DLL文件，测试系统DLL..." << endl;
        
        // 测试一些系统DLL
        const char* testDlls[] = {
            "C:\\Windows\\System32\\kernel32.dll",
            "C:\\Windows\\System32\\user32.dll",
            "C:\\Windows\\System32\\ntdll.dll"
        };
        
        for (int i = 0; i < 3; i++) {
            string info = getLightweightDllInfo(testDlls[i]);
            if (!info.empty()) {
                cout << info << endl;
            }
        }
    } else {
        cout << "找到 " << dllPaths.size() << " 个DLL文件:" << endl;
        for (const auto& dllPath : dllPaths) {
            string info = getLightweightDllInfo(dllPath.c_str());
            if (!info.empty()) {
                cout << info << endl;
            }
        }
    }
    
    cout << "\n=== 轻量级实现特点 ===" << endl;
    cout << "1. 最少的Windows API依赖" << endl;
    cout << "2. 使用C++标准库函数" << endl;
    cout << "3. 基于文件时间生成版本号" << endl;
    cout << "4. 基于文件名生成一致的GUID" << endl;
    cout << "5. 简单的目录扫描功能" << endl;
    
    return 0;
}
