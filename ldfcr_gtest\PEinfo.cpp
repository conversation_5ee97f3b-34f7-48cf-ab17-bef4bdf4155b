#pragma once
#include "PEinfo.h"
#include "ldfcr.h"

PEinfo::PEinfo()
{
	m_pe = nullptr;
	PEinitLib(&m_pe);
	m_p.clear();
	m_dllVec.clear();
}


PEinfo::~PEinfo()
{
	PEfreeLib(m_pe);
}

/**
 * 获取预定义的组件顺序列表
 *
 * 此函数返回26个核心组件的优先级顺序，用于确保输出的一致性。
 * 顺序与libaryVersionInfo中的getOrderedLibs()保持完全一致，
 * 以便在不同平台（Windows/Linux/Mac）间保持统一的组件显示顺序。
 *
 * @return vector<string> 包含26个组件名称的有序列表
 */
const vector<string> PEinfo::getOrderedLibs() {
	static const vector<string> orderedLibs = {
		"ldfcr", "DlpPolicyEngine", "KWRuleEngine", "RegexRuleEngine",
		"FilePropEngine", "FileFpRuleEngine", "SVMRuleEngine", "FpDbRuleEngine",
		"TrCadFilter", "TrCompressFilter", "TrOLEFilter", "TrOOXMLFilter",
		"TrPdfFilter", "TrRtfFilter", "TrTextExtractor", "TrTxtFilter",
		"TrArchive", "TrODFFilter", "DlpOCR", "DlpSCR", "TrOCRFilter",
		"DlpULog", "trcrt", "lua", "pcre", "svm", "jieba", "xcrbnfa", "memstream"
	};
	return orderedLibs;
}

void PEinfo::GetPEinfo()
{
	vector<wstring> wstrfileVec;
	vector<string> strfileVec;
	/*GetLdfcrRunDll();*/

	wstring wstrCurPath = GetCurModuleFile();
	getCurFiles(wstrCurPath, wstrfileVec);
	for (size_t i = 0; i < wstrfileVec.size(); i++)
	{
		//if (!judgeFileIsWorkPath(wstrfileVec[i].c_str()))
		//{
		//	continue;
		//}
		InsertPEInfo(wstrfileVec[i].c_str(), strfileVec);
	}

	//pdf输出结果
	printf("\n");
	printfVec(strfileVec);
}

wstring PEinfo::GetCurModuleFile()
{
	wchar_t cstrCurPath[MAX_PATH];
	GetModuleFileName(NULL, cstrCurPath, MAX_PATH);
	wstring wstrCurPath = cstrCurPath;
	int cstrpos = wstrCurPath.find_last_of(L"\\");
	wstrCurPath = wstrCurPath.substr(0, cstrpos);
	return wstrCurPath;
}

void PEinfo::getCurFiles(wstring & v_wstrPath, vector<wstring>& v_vecFiles)
{
	// 文件句柄
	long long hFile = 0;
	// 文件信息
	struct _wfinddata_t fileinfo;

	if ((hFile = _wfindfirst(m_p.assign(v_wstrPath).append(L"\\*").c_str(), &fileinfo)) != -1) {
		do {
			if ((wcscmp(fileinfo.name, L".") == 0) || (wcscmp(fileinfo.name, L"..") == 0))  //过滤掉本代表本目录的.和上一级目录的.
			{
				continue;
			}
			// 保存文件的全路径
			if (JudgeFileLogic(fileinfo.name))
			{
				v_vecFiles.push_back(m_p.assign(v_wstrPath).append(L"\\").append(fileinfo.name));
			}

		} while (_wfindnext(hFile, &fileinfo) == 0);  //寻找下一个，成功返回0，否则-1

		_findclose(hFile);
	}
}

bool PEinfo::JudgeFileLogic(const wchar_t * v_wstrFilePath)
{
	wstring wstrFileName = v_wstrFilePath;
	string strSrcIni = ".\\PETargetFile.ini";
	char LP[1024];
	for (int i = 0; i < 100; i++)
	{
		string name = "ProjectName";
		name.append(to_string(i));
		GetPrivateProfileStringA("project", name.c_str(), "NULL", LP, 512, strSrcIni.c_str());
		string strCm = WstringToString(wstrFileName);
		if (strcmp(LP, strCm.c_str()) == 0)
		{
			return true;
		}
	}
	return false;
}

void PEinfo::InsertPEInfo(const wchar_t * v_wstrFilePath, vector<string>& v_vecFiles)
{
	char chFileName[23] = { 0 };
	char chFileVersion[15] = { 0 };
	char chFileUtime[20] = { 0 };
	char chFileGuid[41] = { 0 };

	if (!m_pe->CheckFile(v_wstrFilePath))
	{
		wcout << v_wstrFilePath << " check error" << endl;
	}
	char * chFilePE = new char[128];

	m_pe->getFilePath(chFilePE);
	string fileName = srcFileName(chFilePE);
	snprintf(chFileName, sizeof(chFileName), "%-23s", fileName.c_str());
	string strFileName = chFileName;

	m_pe->getFileSoftVersion(chFilePE);
	snprintf(chFileVersion, sizeof(chFileVersion), "%-15s", chFilePE);
	string strGoalVersion = chFileVersion;

	m_pe->getFileCreateTime(chFilePE);
	snprintf(chFileUtime, sizeof(chFileUtime), "%-15s", chFilePE);
	string strGoalUtime = chFileUtime;

	m_pe->getFileGuid(chFilePE);
	snprintf(chFileGuid, sizeof(chFileGuid), "%40s", chFilePE);
	string strGuid = chFileGuid;

	string PERes = strFileName + strGoalVersion + strGoalUtime + strGuid;
	v_vecFiles.push_back(PERes);
}

void PEinfo::printfVec(vector<string>& v_vecFiles)
{
	vector<string>::iterator it = v_vecFiles.begin();
	for (; it != v_vecFiles.end(); ++it)
	{
		cout << *it << endl;
	}
}

void PEinfo::GetLdfcrRunDll()
{
	ILDFcr*   m_pILDFcr;
	if (ldfcr_InitStartup())
	{
		ldfcr_CreateInstance((void **)&m_pILDFcr);
		if (NULL == m_pILDFcr)
		{
			printf("ldfcr init error\n");
			ldfcr_StopRelease();
			return;
		}
	}
	InsertModules();
	ldfcr_StopRelease();
}

void PEinfo::InsertModules()
{
	DWORD dwpid = GetCurrentProcessId();
	//提升进程权限
	//打开令牌
	HANDLE hToken;//创建令牌句柄
	if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ALL_ACCESS | TOKEN_QUERY, &hToken))//打开令牌句柄，设置令牌权限
	{
		printf("OpenProcessToken Failed.");
		return;
	}
	TOKEN_PRIVILEGES tkp;
	LookupPrivilegeValue(NULL, SE_SHUTDOWN_NAME, &tkp.Privileges[0].Luid);//查看令牌权限
	tkp.PrivilegeCount = 1;
	tkp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
	AdjustTokenPrivileges(hToken, FALSE, &tkp, 0, (PTOKEN_PRIVILEGES)NULL, 0);
	if (GetLastError() != ERROR_SUCCESS)
	{
		printf("AdjustTokenPrivileges Failed.");
		return;
	}


	HMODULE hMods[1024];
	HANDLE hProcess;
	DWORD cbNeeded;
	unsigned int i;

	// Print the process identifier.

	/*printf("\nProcess ID: %u\n", dwpid);*/

	// Get a handle to the process.

	hProcess = OpenProcess(PROCESS_QUERY_INFORMATION |
		PROCESS_VM_READ,
		FALSE, dwpid);
	if (NULL == hProcess)
	{
		printf("hProcess is nullptr\n");
		return;
	}

	// Get a list of all the modules in this process.

	if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded))
	{
		for (i = 0; i < (cbNeeded / sizeof(HMODULE)); i++)
		{
			wchar_t szModName[MAX_PATH];

			// Get the full path to the module's file.

			if (GetModuleFileNameEx(hProcess, hMods[i], szModName,
				sizeof(szModName) / sizeof(TCHAR)))
			{
				// Print the module name and handle value.

				m_dllVec.push_back(szModName);
			}
		}
	}

	// Release the handle to the process.

	CloseHandle(hProcess);

	return;
}

bool PEinfo::judgeFileIsWorkPath(const wchar_t * v_wchFilePath)
{
	if (m_dllVec.empty())
	{
		cout << "dll vec get error" << endl;
		return false;
	}
	for (size_t i = 0; i < m_dllVec.size(); ++i)
	{
		if (wcscmp(v_wchFilePath, m_dllVec[i].c_str()) == 0)
		{
			return true;
		}
	}
	wcout << v_wchFilePath << " not found in the running path" << endl;
	return false;
}

std::string PEinfo::WstringToString(const wstring v_wstr)
{
	// wstring转string
	unsigned len = v_wstr.size() * 4;
	setlocale(LC_CTYPE, "");
	char *p = new char[len];
	wcstombs(p, v_wstr.c_str(), len);
	std::string str1(p);
	delete[] p;
	return str1;
}

std::wstring PEinfo::StringToWstring(const string v_str)
{
	wchar_t cstrCurPath[MAX_PATH];
	GetModuleFileName(NULL, cstrCurPath, MAX_PATH);
	std::wstring wstrCurPath = cstrCurPath;
	int cstrpos = wstrCurPath.find_last_of(L"\\");
	wstrCurPath = wstrCurPath.substr(0, cstrpos);
	return wstrCurPath;
}

std::string PEinfo::srcFileName(const std::string v_wstrPath)
{
	char chPath_buffer[_MAX_PATH];
	char chDrive[_MAX_DRIVE];
	char chDir[_MAX_DIR];
	char chFname[_MAX_FNAME];
	char chExt[_MAX_EXT];
	char chPath[_MAX_EXT];
	//wcscpy(chPath_buffer, __argv[0]);
	_splitpath(v_wstrPath.c_str(), chDrive, chDir, chFname, chExt);
	// Note: _wsplitpath is deprecated; consider using _splitpath_s instead
	std::string strRes = chFname;
	strRes.append(chExt);
	return strRes;
}

/**
 * 按预定义顺序输出组件信息
 *
 * 此函数用于在程序启动和结束时输出基线库信息，支持动态对比。
 * 与原GetPEinfo()方法的区别：
 * 1. 按getOrderedLibs()定义的优先级顺序输出，而非发现顺序
 * 2. 为每个组件添加序号（1. 2. 3. ...）
 * 3. 在每个组件下一行显示完整路径（Location: 完整路径）
 * 4. 只输出实际存在的组件，跳过未找到的组件
 *
 * 输出格式：
 * 1. 组件名.dll    版本号    时间    GUID
 *    Location: 完整路径
 * 2. 下一个组件...
 *
 * 用途：用于程序头部和尾部的基线库信息对比，便于检测运行过程中
 *       动态加载的组件变化。
 */
void PEinfo::GetPEinfoOrdered()
{
	vector<wstring> wstrfileVec;
	vector<string> strfileVec;
	vector<string> strPathVec;  // 存储完整路径

	wstring wstrCurPath = GetCurModuleFile();
	getCurFiles(wstrCurPath, wstrfileVec);

	// 收集所有DLL信息和路径
	for (size_t i = 0; i < wstrfileVec.size(); i++)
	{
		InsertPEInfo(wstrfileVec[i].c_str(), strfileVec);
		// 存储完整路径
		string fullPath = WstringToString(wstrfileVec[i]);
		strPathVec.push_back(fullPath);
	}

	// 按照预定义顺序输出
	const vector<string>& orderedLibs = getOrderedLibs();
	int sequenceNumber = 1;

	for (const auto& libName : orderedLibs) {
		// 查找匹配的组件
		for (int i = 0; i < strfileVec.size(); ++i) {
			string fileName = srcFileName(strPathVec[i]);
			// 移除.dll和.exe扩展名
			if (fileName.length() > 4) {
				if (fileName.substr(fileName.length() - 4) == ".dll" ||
					fileName.substr(fileName.length() - 4) == ".exe") {
					fileName = fileName.substr(0, fileName.length() - 4);
				}
			}

			// 检查是否匹配当前要查找的库
			if (fileName == libName ||
				fileName.find(libName) != string::npos) {

				// 输出序号和组件信息（添加换行）
				printf("%d. %s\n", sequenceNumber, strfileVec[i].c_str());

				// 下一行输出完整路径
				printf("   Location: %s\n", strPathVec[i].c_str());

				sequenceNumber++;
				break;  // 找到匹配项后跳出内层循环
			}
		}
	}
}
