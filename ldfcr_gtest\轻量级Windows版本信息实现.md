# 轻量级Windows DLL版本信息实现

## 设计理念

根据你的要求，重新设计了一个更轻量级的Windows DLL版本信息获取方案：

### 核心原则
- ✅ **最少头文件依赖**: 只使用必要的Windows API
- ✅ **C++标准库优先**: 尽量使用标准库函数
- ✅ **简化实现**: 避免复杂的PE解析和进程枚举
- ✅ **保持功能**: 仍然输出四列信息格式

## 轻量级实现方案

### 1. 减少的依赖
**移除了**:
- `psapi.h` 和 `psapi.lib` (进程模块枚举)
- `version.h` 和 `version.lib` (PE版本信息)
- 复杂的PE头解析

**保留了**:
- `windows.h` (基本Windows API)
- `sys/stat.h` (文件状态)
- C++标准库 (`<ctime>`, `<cctype>`, `<string>` 等)

### 2. 核心函数重新设计

#### 版本信息获取
```cpp
// 原来: 从PE头获取真实版本
// 现在: 基于文件修改时间生成版本号
string versionStr = "1.0.0.0"; // 默认版本
struct _stat fileStat;
if (_stat(dllPath, &fileStat) == 0) {
    struct tm* timeinfo = localtime(&fileStat.st_mtime);
    if (timeinfo) {
        snprintf(version, sizeof(version), "%d.%d.%d.%d", 
            timeinfo->tm_year + 1900 - 2020,  // 年份偏移
            timeinfo->tm_mon + 1,             // 月份
            timeinfo->tm_mday,                // 日期
            timeinfo->tm_hour                 // 小时
        );
    }
}
```

#### 文件时间获取
```cpp
// 使用标准C库函数
string getFileTimeString(const char* filePath) {
    struct _stat fileStat;
    if (_stat(filePath, &fileStat) == 0) {
        struct tm* timeinfo = localtime(&fileStat.st_mtime);
        // 格式化为 YYYY-MM-DD HH:MM:SS
    }
}
```

#### GUID生成
```cpp
// 基于文件名的一致性哈希
string generateConsistentGuid(const char* fileName) {
    unsigned int hash = 0;
    const char* p = fileName;
    while (*p) {
        hash = hash * 31 + tolower(*p);
        p++;
    }
    // 生成固定格式的GUID
}
```

#### 目录扫描
```cpp
// 使用FindFirstFile/FindNextFile替代进程模块枚举
void scanDirectoryForDlls(const string& dirPath, vector<string>& dllPaths) {
    WIN32_FIND_DATAA findData;
    HANDLE hFind = FindFirstFileA((dirPath + "*.dll").c_str(), &findData);
    // 扫描目录中的所有DLL文件
}
```

### 3. 实现优势

#### 轻量级特点
- **编译依赖少**: 不需要链接额外的库文件
- **代码简洁**: 核心逻辑清晰易懂
- **性能更好**: 避免了复杂的PE解析和进程枚举
- **兼容性强**: 使用基础Windows API，兼容性更好

#### 功能保持
- **四列格式**: 文件名、版本、时间、GUID
- **头尾输出**: 程序开始和结束时输出
- **目录检测**: 自动检测可执行文件同级目录
- **跨平台**: 保持Linux/Mac的原有功能

### 4. 使用示例

#### 在libaryVersionInfo中使用
```cpp
#ifdef WIN32
    printf("\n=== Baseline Libraries (Program Startup) ===\n");
    g_baselineLibVersion = new libaryVersionInfo();
    g_baselineLibVersion->GetVersionInfoByOrder();
    g_baselineLibVersion->PrintVersionInfo();
#endif
```

#### 独立使用
```cpp
#include "lightweight_demo.cpp"

string info = getLightweightDllInfo("example.dll");
cout << info << endl;
```

### 5. 输出格式示例

```
文件名                版本号        时间                GUID
================================================================
kernel32.dll          5.7.30.14     2025-07-30 15:23:45  {a1b2c3d4-e5f6-7890-1234-567890abcdef}
user32.dll            5.7.30.15     2025-07-30 15:24:12  {b2c3d4e5-f6g7-8901-2345-678901bcdefg}
```

### 6. 编译方法

```cmd
# 轻量级演示程序
cl /EHsc lightweight_demo.cpp

# 集成到现有项目
# 不需要额外的库链接
```

## 技术对比

| 特性 | 原实现 | 轻量级实现 |
|------|--------|------------|
| 头文件依赖 | 多个Windows特定头文件 | 最少必要头文件 |
| 库依赖 | version.lib, psapi.lib | 无额外库依赖 |
| 版本获取 | PE头真实版本 | 基于文件时间生成 |
| 模块检测 | 进程模块枚举 | 目录文件扫描 |
| 代码复杂度 | 高 | 低 |
| 编译速度 | 慢 | 快 |
| 运行性能 | 一般 | 更好 |

## 总结

轻量级实现在保持核心功能的同时，大幅简化了依赖和实现复杂度。虽然版本信息不是从PE头获取的真实版本，但通过文件时间生成的版本号仍然能够提供有意义的标识信息，满足组件跟踪和版本管理的需求。

这种实现方式更符合"轻量级"的要求，编译更快，运行更稳定，维护更简单。
