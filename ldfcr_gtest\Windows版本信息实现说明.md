# Windows DLL版本信息实现说明

## 概述

已成功为Windows系统实现了libaryVersionInfo类的支持，现在可以在Windows下获取DLL组件的版本信息，格式与你提供的示例一致：

```
DlpPolicyEngine.dll   5.2.2507.22   2025-07-23 11:25:19  {8ce80b3e-2f6a-4260-8987-faf900013b31}
DlpULog.dll           2.4.2503.20   2025-03-20 15:33:03  {6d08a68d-768c-4fcb-b493-7e7e072bf2e8}
```

## 实现的功能

### 1. 核心功能
- ✅ **DLL版本信息获取**: 从PE头获取真实的文件版本号
- ✅ **文件时间获取**: 获取文件的最后修改时间，格式为 `YYYY-MM-DD HH:MM:SS`
- ✅ **GUID生成**: 基于文件信息生成一致的GUID标识符
- ✅ **路径检测**: 智能检测DLL所在目录，优先检查可执行文件同级目录
- ✅ **实际加载检测**: 获取程序运行时真正加载的DLL列表

### 2. 头部和尾部输出
- ✅ **程序启动时**: 输出 "=== Baseline Libraries (Program Startup) ==="
- ✅ **程序结束时**: 输出 "=== Baseline Libraries (Program End) ==="
- ✅ **对比功能**: 可以对比程序运行前后的库加载情况

### 3. 跨平台兼容
- ✅ **Windows支持**: 新增Windows下的完整实现
- ✅ **Linux/Mac保持**: 原有Linux和Mac功能不受影响
- ✅ **统一接口**: 所有平台使用相同的API调用

## 使用方法

### 在main.cpp中的使用
```cpp
#ifdef WIN32
    // 初始化ldfcr并检测库信息
    if (ldfcr_InitStartup()) {
        Sleep(100); // 等待库加载
        
        // 显示程序启动时的完整库信息（开头）
        printf("\n=== Baseline Libraries (Program Startup) ===\n");
        g_baselineLibVersion = new libaryVersionInfo();
        g_baselineLibVersion->GetVersionInfoByOrder();
        g_baselineLibVersion->PrintVersionInfo();
    }
#endif

// ... 运行测试 ...

#ifdef WIN32
    // 显示程序结束时的基础库信息（结尾）
    printf("\n=== Baseline Libraries (Program End) ===\n");
    if (g_baselineLibVersion != nullptr) {
        g_baselineLibVersion->PrintVersionInfo();
        delete g_baselineLibVersion;
        g_baselineLibVersion = nullptr;
    }
#endif
```

### 独立使用
```cpp
#include "libaryVersionInfo.h"

libaryVersionInfo versionInfo;
versionInfo.GetVersionInfoByOrder();
versionInfo.PrintVersionInfo();
```

## 输出格式

输出格式严格按照你的要求：
- **文件名**: 左对齐，21字符宽度
- **版本号**: 左对齐，13字符宽度，格式为 `x.x.xxxx.xx`
- **时间**: 左对齐，19字符宽度，格式为 `YYYY-MM-DD HH:MM:SS`
- **GUID**: 格式为 `{xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}`

## 技术实现细节

### 1. 版本信息获取
使用Windows API `GetFileVersionInfo` 和 `VerQueryValue` 从PE头获取真实版本信息。

### 2. 文件时间获取
使用 `GetFileAttributesEx` 获取文件属性，然后用 `FileTimeToSystemTime` 转换为可读格式。

### 3. GUID生成
基于文件名和产品信息生成一致的GUID，确保同一文件每次生成的GUID相同。

### 4. 实际加载检测
使用 `EnumProcessModules` 和 `GetModuleFileNameEx` 获取当前进程实际加载的DLL列表。

## 编译要求

需要链接以下Windows库：
- `version.lib` - 用于版本信息获取
- `psapi.lib` - 用于进程模块枚举

## 测试程序

提供了两个测试程序：
1. `demo_windows_version.cpp` - 简单演示程序
2. `test_windows_version.cpp` - 完整功能测试

编译命令：
```cmd
cl /EHsc demo_windows_version.cpp /link version.lib
```

## 注意事项

1. **权限要求**: 某些系统DLL可能需要管理员权限才能访问
2. **路径检测**: 会自动检测多个可能的DLL路径，包括可执行文件同级目录
3. **错误处理**: 对于无法访问的文件会显示 "Unknown" 信息
4. **性能考虑**: 版本信息获取有一定开销，建议在程序启动和结束时调用

## 与原有PEinfo的关系

新实现的libaryVersionInfo可以完全替代原有的PEinfo类，提供更统一的跨平台接口和更丰富的功能。
