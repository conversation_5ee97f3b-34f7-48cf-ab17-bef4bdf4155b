#pragma once
#include <string>
#include <vector>
#include <iostream>
#include <windows.h>
#include <io.h>
#include <algorithm>
#include <psapi.h>
#include "PE/PE.h"

#pragma comment(lib, "PE/PE.lib")
#pragma comment(lib, "psapi.lib")

using namespace std;
class PEinfo
{
public:
	PEinfo();
	~PEinfo();

public:
	void GetPEinfo();

private:
	wstring GetCurModuleFile();

	void getCurFiles(wstring &v_wstrPath, vector<wstring>& v_vecFiles);//遍历当前目录

	bool JudgeFileLogic(const wchar_t * v_wstrFilePath);

	void InsertPEInfo(const wchar_t * v_wstrFilePath, vector<string>& v_vecFiles);

	void printfVec(vector<string>& v_vecFiles);

	void GetLdfcrRunDll();

	void InsertModules();

	bool judgeFileIsWorkPath(const wchar_t * v_wchFilePath);

private:
	string WstringToString(const wstring v_wstr);

	wstring StringToWstring(const string v_str);

	string srcFileName(const string v_wstrPath);

private:
	wstring m_p;

	vector<wstring> m_dllVec;

	PE * m_pe;
};

