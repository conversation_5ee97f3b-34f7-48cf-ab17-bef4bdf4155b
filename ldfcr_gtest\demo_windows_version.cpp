/*
 * Windows DLL版本信息演示程序
 * 展示如何在Windows下获取DLL组件的版本、时间、GUID等信息
 */

#include <iostream>
#include <windows.h>

// 简化的版本信息获取函数
void getDllVersionInfo(const char* dllPath) {
    std::cout << "正在获取 " << dllPath << " 的版本信息..." << std::endl;
    
    // 检查文件是否存在
    if (GetFileAttributesA(dllPath) == INVALID_FILE_ATTRIBUTES) {
        std::cout << "文件不存在: " << dllPath << std::endl;
        return;
    }
    
    // 获取文件名
    const char* fileName = strrchr(dllPath, '\\');
    if (!fileName) fileName = strrchr(dllPath, '/');
    if (!fileName) fileName = dllPath;
    else fileName++;
    
    // 获取版本信息
    DWORD dwSize = GetFileVersionInfoSizeA(dllPath, NULL);
    if (dwSize > 0) {
        char* pBuf = new char[dwSize];
        if (GetFileVersionInfoA(dllPath, 0, dwSize, pBuf)) {
            VS_FIXEDFILEINFO* pVsInfo;
            UINT uLen;
            if (VerQueryValueA(pBuf, "\\", (void**)&pVsInfo, &uLen)) {
                char version[64];
                snprintf(version, sizeof(version), "%d.%d.%d.%d",
                    HIWORD(pVsInfo->dwFileVersionMS), LOWORD(pVsInfo->dwFileVersionMS),
                    HIWORD(pVsInfo->dwFileVersionLS), LOWORD(pVsInfo->dwFileVersionLS));
                
                // 获取文件时间
                WIN32_FILE_ATTRIBUTE_DATA fileAttr;
                char timeStr[64] = "Unknown";
                if (GetFileAttributesExA(dllPath, GetFileExInfoStandard, &fileAttr)) {
                    SYSTEMTIME st;
                    if (FileTimeToSystemTime(&fileAttr.ftLastWriteTime, &st)) {
                        snprintf(timeStr, sizeof(timeStr), "%04d-%02d-%02d %02d:%02d:%02d",
                            st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                    }
                }
                
                // 生成GUID（简化版）
                DWORD hash = 0;
                const char* p = fileName;
                while (*p) {
                    hash = hash * 31 + tolower(*p);
                    p++;
                }
                char guid[64];
                snprintf(guid, sizeof(guid), "{%08x-%04x-%04x-%04x-%08x%04x}",
                    hash, (hash >> 16) & 0xFFFF, (hash >> 8) & 0xFFFF,
                    hash & 0xFFFF, hash ^ 0x12345678, hash & 0xFFFF);
                
                // 输出格式化信息
                printf("%-21s %-13s %-19s %s\n", fileName, version, timeStr, guid);
            }
        }
        delete[] pBuf;
    } else {
        printf("%-21s %-13s %-19s %s\n", fileName, "Unknown", "Unknown", "{unknown-guid}");
    }
}

int main() {
    std::cout << "=== Windows DLL版本信息演示 ===" << std::endl;
    std::cout << "格式: 文件名                版本号        时间                GUID" << std::endl;
    std::cout << "================================================================" << std::endl;
    
    // 测试一些常见的系统DLL
    const char* testDlls[] = {
        "C:\\Windows\\System32\\kernel32.dll",
        "C:\\Windows\\System32\\user32.dll",
        "C:\\Windows\\System32\\ntdll.dll",
        "C:\\Windows\\System32\\advapi32.dll"
    };
    
    for (int i = 0; i < 4; i++) {
        getDllVersionInfo(testDlls[i]);
    }
    
    std::cout << "\n=== 演示完成 ===" << std::endl;
    std::cout << "这展示了如何获取Windows DLL的版本信息，包括：" << std::endl;
    std::cout << "1. 文件名" << std::endl;
    std::cout << "2. 版本号 (从PE头获取)" << std::endl;
    std::cout << "3. 文件修改时间" << std::endl;
    std::cout << "4. GUID (基于文件名生成)" << std::endl;
    
    return 0;
}
