# Windows进程模块检测实现

## 功能升级

根据用户需求，将Windows下的组件检测方式从**文件系统扫描**升级为**进程模块检测**，与Linux/Mac保持一致。

## 实现对比

### Linux/Mac的做法 ✅
```cpp
// Linux系统获取实际加载的动态库到临时容器
void libaryVersionInfo::getRealLoadedLibrariesLinuxToTemp(vector<string>& libNames, vector<string>& libPaths)

// Mac系统获取实际加载的动态库到临时容器  
void libaryVersionInfo::getRealLoadedLibrariesMacToTemp(vector<string>& libNames, vector<string>& libPaths)
```

**特点**：
- ✅ **检测实际加载的库**：扫描进程内存空间，找到真正被加载的动态库
- ✅ **存储到容器**：将所有检测到的库信息存储到临时容器
- ✅ **一次性输出**：最后统一按顺序输出

### Windows原来的做法 ❌
```cpp
// 只扫描当前目录下的DLL文件
wstring wstrCurPath = GetCurModuleFile();
getCurFiles(wstrCurPath, wstrfileVec);
```

**问题**：
- ❌ **只扫描文件系统**：只查看当前目录下的DLL文件
- ❌ **不检测进程依赖**：没有检测进程实际加载了哪些库
- ❌ **信息不完整**：可能遗漏系统路径下的重要组件

### Windows新的做法 ✅
```cpp
// Windows系统获取实际加载的动态库到临时容器（类似Linux/Mac）
void PEinfo::getRealLoadedLibrariesWindowsToTemp(vector<string>& libNames, vector<string>& libPaths)
```

**特点**：
- ✅ **检测进程模块**：使用`EnumProcessModules()`枚举实际加载的模块
- ✅ **获取完整路径**：使用`GetModuleFileNameEx()`获取模块完整路径
- ✅ **存储到容器**：与Linux/Mac保持一致的数据结构
- ✅ **一次性输出**：统一按预定义顺序输出

## 技术实现

### 核心API使用
```cpp
void PEinfo::getRealLoadedLibrariesWindowsToTemp(vector<string>& libNames, vector<string>& libPaths)
{
    HANDLE hProcess = GetCurrentProcess();
    HMODULE hMods[1024];
    DWORD cbNeeded;
    
    // 枚举当前进程加载的所有模块
    if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded))
    {
        DWORD moduleCount = cbNeeded / sizeof(HMODULE);
        
        for (DWORD i = 0; i < moduleCount; i++)
        {
            char szModPath[MAX_PATH];
            
            // 获取模块完整路径
            if (GetModuleFileNameExA(hProcess, hMods[i], szModPath, sizeof(szModPath)))
            {
                // 提取文件名并存储
                string fullPath = szModPath;
                string fileName = srcFileName(fullPath);
                
                // 移除扩展名
                if (fileName.length() > 4) {
                    if (fileName.substr(fileName.length() - 4) == ".dll" ||
                        fileName.substr(fileName.length() - 4) == ".exe") {
                        fileName = fileName.substr(0, fileName.length() - 4);
                    }
                }
                
                libNames.push_back(fileName);
                libPaths.push_back(fullPath);
            }
        }
    }
}
```

### 关键Windows API
- **`EnumProcessModules()`**：枚举进程加载的所有模块
- **`GetModuleFileNameEx()`**：获取模块的完整文件路径
- **`GetCurrentProcess()`**：获取当前进程句柄

### 数据流程
1. **枚举模块**：获取进程中所有加载的DLL/EXE模块
2. **提取信息**：获取每个模块的完整路径和文件名
3. **存储容器**：将库名和路径分别存储到vector容器
4. **格式化输出**：使用PE库获取详细信息（版本、时间、GUID）
5. **按序输出**：按预定义顺序匹配和输出组件信息

## 优势对比

### 检测范围
**原方式**：
- 只检测当前工作目录下的DLL文件
- 可能遗漏系统目录、程序目录等重要组件

**新方式**：
- 检测进程实际加载的所有模块
- 包括系统DLL、第三方库、程序组件等
- 覆盖完整的依赖关系

### 准确性
**原方式**：
- 文件存在不等于被加载
- 可能包含未使用的DLL文件

**新方式**：
- 只显示真正被加载和使用的组件
- 准确反映程序的实际依赖关系

### 一致性
**原方式**：
- 与Linux/Mac的实现方式不同
- 检测结果可能不一致

**新方式**：
- 与Linux/Mac使用相同的检测逻辑
- 跨平台结果保持一致

## 预期效果

### 更完整的组件检测
现在能够检测到：
- **系统组件**：Windows系统DLL（如kernel32.dll、user32.dll等）
- **运行时库**：Visual C++运行时、.NET框架等
- **第三方库**：所有实际被程序加载的第三方组件
- **程序组件**：程序自身的所有模块

### 更准确的依赖分析
- **真实依赖**：只显示实际被加载的组件
- **完整路径**：显示组件的实际加载位置
- **版本信息**：准确的版本、时间、GUID信息

### 更好的问题诊断
- **依赖缺失**：能够发现缺失的关键依赖
- **版本冲突**：能够检测到版本不匹配问题
- **加载路径**：能够确认组件的实际加载位置

## 总结

通过实现`getRealLoadedLibrariesWindowsToTemp()`函数，Windows下的组件检测功能现在：

- ✅ **与Linux/Mac一致**：使用相同的检测逻辑和数据结构
- ✅ **检测更准确**：只显示实际加载的组件，不是文件系统中的文件
- ✅ **信息更完整**：覆盖所有实际的依赖关系
- ✅ **诊断更有效**：提供真实的组件加载状态

这个升级使得Windows下的组件信息输出功能更加强大和实用！
