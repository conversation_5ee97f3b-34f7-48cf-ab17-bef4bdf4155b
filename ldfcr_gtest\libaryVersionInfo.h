#ifndef LIBARY_VERSION_H
#define LIBARY_VERSION_H


// 输出so文件version信息
// by zhangkaiqin 2024 5 9 
// 对外接口 GetVersionInfo 运行功能 PrintVersionInfo 输出信息
// 调用组件中协定加入固定version信息 如 
// const char* lib_info = "RegexRuleEngine build version 3.52.240507 on 2024-05-07 16:50";
// 核心功能调用popen创建管道子进程打印version信息相关数据

#include <stdio.h>
#include <string.h>
#include <vector>
#include <string>

#ifdef _WIN32
#include <windows.h>
#include <direct.h>  // for _getcwd
#include <io.h>
#include <sys/stat.h>
#else
#include <unistd.h>  // for getcwd and Mac system calls
#endif

#include <fstream>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <cctype>  // for tolower

#ifdef __APPLE__
#include <sys/types.h>
#endif

using namespace std;

class libaryVersionInfo
{
private:
	// 预定义的组件顺序（静态常量，避免重复定义）
	static const vector<string> getOrderedLibs() {
		static const vector<string> orderedLibs = {
			"ldfcr", "DlpPolicyEngine", "KWRuleEngine", "RegexRuleEngine",
			"FilePropEngine", "FileFpRuleEngine", "SVMRuleEngine", "FpDbRuleEngine",
			"TrCadFilter", "TrCompressFilter", "TrOLEFilter", "TrOOXMLFilter",
			"TrPdfFilter", "TrRtfFilter", "TrTextExtractor", "TrTxtFilter",
			"TrArchive", "TrODFFilter", "DlpOCR", "DlpSCR", "TrOCRFilter",
			"DlpULog", "trcrt", "lua", "pcre", "svm", "jieba", "xcrbnfa", "memstream"
		};
		return orderedLibs;
	}



private:
	bool compareByPattern(const string v_charPattern, const string v_charMatch)
	{
		int leng = v_charPattern.size();
		string strSub = v_charMatch.substr(0, leng);
		if (strcmp(v_charPattern.c_str(), strSub.c_str()) == 0)
		{
			return true;
		}
		return false;
	}

	void getSensitiveSoVersion(const char * v_char)
	{
		vector<string> vec;
		string str = v_char;
		string strInstruction;
		string libPath = detectLibraryPath();

#ifdef _WIN32
		// Windows下直接使用PE信息获取
		string dllPath = libPath + str + ".dll";
		string versionInfo = getWindowsDllVersionInfo(dllPath.c_str());
		if (!versionInfo.empty()) {
			m_vec.push_back(versionInfo);
		} else {
			string strError = str + " no version number has been established \n";
			m_vec.push_back(strError);
		}
		return;
#elif __linux__
		if (str == "TrArchive") {
			strInstruction = "strings " + libPath + str + ".so | grep \"build version\"";
		}
		else {
			strInstruction = "strings " + libPath + "lib" + str + ".so | grep \"build version\"";
		}
#elif __APPLE__
		if (str == "TrArchive") {
			strInstruction = "strings " + libPath + str + ".dylib | grep \"build version\"";
		}
		else {
			strInstruction = "strings " + libPath + "lib" + str + ".dylib | grep \"build version\"";
		}
#endif

		FILE *fp;

		fp = popen(strInstruction.c_str(), "r");

		if (fp == NULL) {
			printf("Failed to run\n");
			return;
		}

		char buffer[1024];
		while (fgets(buffer, sizeof(buffer), fp) != NULL) {
			vec.push_back(buffer);
		}
		pclose(fp);

		bool matchRes = false;
		for (int i = 0; i < vec.size(); ++i)
		{
			bool res = compareByPattern(v_char, vec[i]);
			if (res)
			{
				m_vec.push_back(vec[i]);
				matchRes = true;
				break;
			}
		}
		if (!matchRes)
		{
			string strError = str + " no version number has been established \n";
			m_vec.push_back(strError);
		}
		return;
	}

	vector<string> m_vec;
	vector<string> m_libPaths;
	vector<string> m_libVec;

public:
	libaryVersionInfo(/* args */);
	~libaryVersionInfo();

	void GetVersionInfo()
	{
		m_vec.clear();
		for (int i = 0; i < m_libVec.size(); ++i)
		{
			getSensitiveSoVersion(m_libVec[i].c_str());
		}
	}

	void GetVersionInfoByOrder();

	void PrintVersionInfo();

#ifdef _WIN32
	// 公共测试函数，用于测试单个DLL信息获取
	string testGetDllVersionInfo(const char* dllPath) {
		return getWindowsDllVersionInfo(dllPath);
	}
#endif
	{
		for (int i = 0; i < m_vec.size(); ++i)
		{
			printf("%s", m_vec[i].c_str());

			if (i < m_libPaths.size()) {
				string fullPath = m_libPaths[i];

				// 如果是相对路径，转换为绝对路径
#ifdef _WIN32
				if (fullPath.length() < 2 || fullPath[1] != ':') {
					char currentDir[1024];
					if (_getcwd(currentDir, sizeof(currentDir)) != NULL) {
						fullPath = string(currentDir) + "\\" + fullPath;
					}
				}
#else
				if (fullPath[0] != '/') {
					char currentDir[1024];
					if (getcwd(currentDir, sizeof(currentDir)) != NULL) {
						fullPath = string(currentDir) + "/" + fullPath;
					}
				}
#endif

				printf("   Location: %s\n", fullPath.c_str());
			}
		}
	}

private:
	// 跨平台库路径检测
	string detectLibraryPath()
	{
		vector<string> possiblePaths;

#ifdef _WIN32
		// Windows下优先检查可执行文件所在目录
		char exePath[MAX_PATH];
		if (GetModuleFileNameA(NULL, exePath, MAX_PATH)) {
			char* lastSlash = strrchr(exePath, '\\');
			if (lastSlash) {
				*lastSlash = '\0';
				possiblePaths.push_back(string(exePath) + "\\");
			}
		}
		possiblePaths.insert(possiblePaths.end(), {
			"libs_x64\\",
			"lib\\dlpcomm\\",
			"..\\libs_x64\\",
			"..\\lib\\dlpcomm\\",
			".\\",
			"libs_x64/",
			"lib/dlpcomm/",
			"../libs_x64/",
			"../lib/dlpcomm/"
		});
#else
		possiblePaths = {
			"libs_x64/",
			"lib/dlpcomm/",
			"../libs_x64/",
			"../lib/dlpcomm/"
		};
#endif

		for (const auto& path : possiblePaths) {
#ifdef _WIN32
			string testFile = path + "trcrt.dll";
			if (_access(testFile.c_str(), 0) == 0) {
				return path;
			}

			testFile = path + "DlpPolicyEngine.dll";
			if (_access(testFile.c_str(), 0) == 0) {
				return path;
			}
#elif __linux__
			string testFile = path + "libtrcrt.so";
			FILE* file = fopen(testFile.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				return path;
			}

			testFile = path + "TrArchive.so";
			FILE* file2 = fopen(testFile.c_str(), "r");
			if (file2 != NULL) {
				fclose(file2);
				return path;
			}
#elif __APPLE__
			string testFile = path + "libtrcrt.dylib";
			FILE* file = fopen(testFile.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				return path;
			}

			testFile = path + "TrArchive.dylib";
			FILE* file2 = fopen(testFile.c_str(), "r");
			if (file2 != NULL) {
				fclose(file2);
				return path;
			}
#endif
		}

		printf("Warning: No valid library path found, using default lib/dlpcomm/\n");
		return "lib/dlpcomm/";
	}

	void extractLibNameFromPath(const string& fullPath, vector<string>& libNames);

	bool isTargetLibrary(const string& libName, const string& fullPath);

#ifdef _WIN32
	// Windows下轻量级版本信息获取
	string getWindowsDllVersionInfo(const char* dllPath);
	string getFileTimeString(const char* filePath);
	string generateConsistentGuid(const char* fileName);
	void scanDirectoryForDlls(const string& dirPath, vector<string>& libNames, vector<string>& libPaths);
#endif

#ifdef __APPLE__
	// 临时容器
	void getRealLoadedLibrariesMacToTemp(vector<string>& libNames, vector<string>& libPaths);
#endif

#if __linux__
	// 临时容器
	void getRealLoadedLibrariesLinuxToTemp(vector<string>& libNames, vector<string>& libPaths);
#endif
};

libaryVersionInfo::libaryVersionInfo()
{
	m_vec.clear();
	m_libPaths.clear();
	m_libVec = getOrderedLibs(); // 直接使用静态函数获取有序列表
}

libaryVersionInfo::~libaryVersionInfo()
{
}

// 按指定顺序获取实际加载的库信息
void libaryVersionInfo::GetVersionInfoByOrder()
{
	m_vec.clear();
	m_libPaths.clear();

	const vector<string>& orderedLibs = getOrderedLibs();

	vector<string> detectedLibs;
	vector<string> detectedPaths;

#ifdef _WIN32
	// Windows下扫描可能的DLL目录
	string libPath = detectLibraryPath();
	scanDirectoryForDlls(libPath, detectedLibs, detectedPaths);
#elif __APPLE__
	getRealLoadedLibrariesMacToTemp(detectedLibs, detectedPaths);
#elif __linux__
	getRealLoadedLibrariesLinuxToTemp(detectedLibs, detectedPaths);
#endif

	for (const auto& libName : orderedLibs) {
		for (size_t i = 0; i < detectedLibs.size(); ++i) {
			if (detectedLibs[i] == libName) {
				getSensitiveSoVersion(libName.c_str());
				m_libPaths.push_back(detectedPaths[i]);
				break;
			}
		}
	}

	if (m_vec.empty()) {
		printf("Warning: No loaded libraries detected, using traditional method\n");
		string libPath = detectLibraryPath();

		for (const auto& libName : orderedLibs) {
			string fullPath;
#ifdef _WIN32
			fullPath = libPath + libName + ".dll";
			if (_access(fullPath.c_str(), 0) == 0) {
				getSensitiveSoVersion(libName.c_str());
				m_libPaths.push_back(fullPath);
			}
#elif __linux__
			if (libName == "TrArchive") {
				fullPath = libPath + libName + ".so";
			}
			else {
				fullPath = libPath + "lib" + libName + ".so";
			}
			FILE* file = fopen(fullPath.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				getSensitiveSoVersion(libName.c_str());
				m_libPaths.push_back(fullPath);
			}
#elif __APPLE__
			if (libName == "TrArchive") {
				fullPath = libPath + libName + ".dylib";
			}
			else {
				fullPath = libPath + "lib" + libName + ".dylib";
			}
			FILE* file = fopen(fullPath.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				getSensitiveSoVersion(libName.c_str());
				m_libPaths.push_back(fullPath);
			}
#endif
		}
	}
}



// 统一的路径提取函数，支持跨平台
void libaryVersionInfo::extractLibNameFromPath(const string& fullPath, vector<string>& libNames)
{
	size_t lastSlash = fullPath.find_last_of('/');
	size_t lastBackslash = fullPath.find_last_of('\\');
	size_t lastSeparator = std::string::npos;

	if (lastSlash != std::string::npos && lastBackslash != std::string::npos) {
		lastSeparator = std::max(lastSlash, lastBackslash);
	} else if (lastSlash != std::string::npos) {
		lastSeparator = lastSlash;
	} else if (lastBackslash != std::string::npos) {
		lastSeparator = lastBackslash;
	}

	if (lastSeparator == std::string::npos) return;

	std::string fileName = fullPath.substr(lastSeparator + 1);

#ifdef _WIN32
	size_t dllPos = fileName.find(".dll");
	if (dllPos != std::string::npos) {
		fileName = fileName.substr(0, dllPos);
	}
#elif __linux__
	size_t versionPos = fileName.find(".so.");
	if (versionPos != std::string::npos) {
		fileName = fileName.substr(0, versionPos + 3);
	}
	if (fileName.length() > 3 && fileName.substr(fileName.length() - 3) == ".so") {
		fileName = fileName.substr(0, fileName.length() - 3);
	}
#elif __APPLE__
	size_t dylibPos = fileName.find(".dylib");
	if (dylibPos != std::string::npos) {
		fileName = fileName.substr(0, dylibPos);
	}
#endif

	// 只在非Windows系统下移除lib前缀
#ifndef _WIN32
	if (fileName.length() > 3 && fileName.substr(0, 3) == "lib") {
		fileName = fileName.substr(3);
	}
#endif

	if (!fileName.empty()) {
		libNames.push_back(fileName);
	}
}

// 检查是否为目标库
bool libaryVersionInfo::isTargetLibrary(const string& libName, const string& fullPath)
{
	for (const auto& targetLib : m_libVec) {
		if (libName == targetLib) {
			return true;
		}
	}
	return false;
}



#ifdef __APPLE__
// Mac系统获取实际加载的动态库到临时容器
void libaryVersionInfo::getRealLoadedLibrariesMacToTemp(vector<string>& libNames, vector<string>& libPaths)
{
	// Mac系统使用lsof命令获取加载的动态库，比vmmap更可靠
	string cmd = "lsof -p " + to_string(getpid()) + " 2>/dev/null | grep '\\.dylib' | awk '{for(i=9;i<=NF;i++) printf \"%s \", $i; print \"\"}' | sed 's/ $//' | sort | uniq";

	FILE* pipe = popen(cmd.c_str(), "r");
	if (pipe == NULL) {
		printf("Warning: Failed to execute lsof command on Mac\n");
		return;
	}

	char line[1024];
	while (fgets(line, sizeof(line), pipe) != NULL) {
		string fullPath = string(line);
		if (!fullPath.empty() && fullPath.back() == '\n') {
			fullPath.pop_back();
		}

		if (!fullPath.empty()) {
			std::vector<std::string> extractedLibNames;
			extractLibNameFromPath(fullPath, extractedLibNames);

			for (const auto& libName : extractedLibNames) {
				if (isTargetLibrary(libName, fullPath)) {
					libNames.push_back(libName);
					libPaths.push_back(fullPath);
				}
			}
		}
	}

	pclose(pipe);
}
#endif

#if __linux__
// Linux系统获取实际加载的动态库到临时容器
void libaryVersionInfo::getRealLoadedLibrariesLinuxToTemp(vector<string>& libNames, vector<string>& libPaths)
{
	FILE* file = fopen("/proc/self/maps", "r");
	if (file == NULL) {
		printf("Warning: Failed to open /proc/self/maps on Linux\n");
		return;
	}

	char line[1024];
	while (fgets(line, sizeof(line), file) != NULL) {
		string mapLine = string(line);

		if (mapLine.find(".so") != string::npos) {
			size_t lastSpace = mapLine.find_last_of(' ');
			if (lastSpace != string::npos) {
				string fullPath = mapLine.substr(lastSpace + 1);
				if (!fullPath.empty() && fullPath.back() == '\n') {
					fullPath.pop_back();
				}

				if (!fullPath.empty()) {
					std::vector<std::string> extractedLibNames;
					extractLibNameFromPath(fullPath, extractedLibNames);

					for (const auto& libName : extractedLibNames) {
						if (isTargetLibrary(libName, fullPath)) {
							bool exists = false;
							for (const auto& existingLib : libNames) {
								if (existingLib == libName) {
									exists = true;
									break;
								}
							}
							if (!exists) {
								libNames.push_back(libName);
								libPaths.push_back(fullPath);
							}
						}
					}
				}
			}
		}
	}

	fclose(file);
}
#endif

#ifdef _WIN32
// Windows系统轻量级DLL版本信息获取
string libaryVersionInfo::getWindowsDllVersionInfo(const char* dllPath)
{
	// 检查文件是否存在
	if (_access(dllPath, 0) != 0) {
		return "";
	}

	// 提取文件名
	const char* lastSlash = strrchr(dllPath, '\\');
	const char* lastSlash2 = strrchr(dllPath, '/');
	const char* baseName = dllPath;
	if (lastSlash && (!lastSlash2 || lastSlash > lastSlash2)) {
		baseName = lastSlash + 1;
	} else if (lastSlash2) {
		baseName = lastSlash2 + 1;
	}

	// 获取文件时间
	string timeStr = getFileTimeString(dllPath);

	// 生成一致的GUID
	string guidStr = generateConsistentGuid(baseName);

	// 简化的版本获取 - 使用文件修改时间作为版本标识
	string versionStr = "1.0.0.0"; // 默认版本

	// 尝试从文件修改时间生成版本号
	struct _stat fileStat;
	if (_stat(dllPath, &fileStat) == 0) {
		struct tm* timeinfo = localtime(&fileStat.st_mtime);
		if (timeinfo) {
			char version[32];
			snprintf(version, sizeof(version), "%d.%d.%d.%d",
				timeinfo->tm_year + 1900 - 2020,  // 年份偏移
				timeinfo->tm_mon + 1,             // 月份
				timeinfo->tm_mday,                // 日期
				timeinfo->tm_hour                 // 小时
			);
			versionStr = version;
		}
	}

	// 格式化输出
	char result[512];
	snprintf(result, sizeof(result), "%-21s %-13s %-19s %s",
		baseName, versionStr.c_str(), timeStr.c_str(), guidStr.c_str());

	return string(result);
}

// 轻量级文件时间获取
string libaryVersionInfo::getFileTimeString(const char* filePath)
{
	struct _stat fileStat;
	if (_stat(filePath, &fileStat) == 0) {
		struct tm* timeinfo = localtime(&fileStat.st_mtime);
		if (timeinfo) {
			char timeStr[64];
			snprintf(timeStr, sizeof(timeStr), "%04d-%02d-%02d %02d:%02d:%02d",
				timeinfo->tm_year + 1900, timeinfo->tm_mon + 1, timeinfo->tm_mday,
				timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec);
			return string(timeStr);
		}
	}
	return "Unknown";
}

// 基于文件名生成一致的GUID
string libaryVersionInfo::generateConsistentGuid(const char* fileName)
{
	// 使用简单的哈希算法生成一致的GUID
	unsigned int hash = 0;
	const char* p = fileName;
	while (*p) {
		hash = hash * 31 + tolower(*p);
		p++;
	}

	// 生成固定格式的GUID
	char guid[64];
	snprintf(guid, sizeof(guid), "{%08x-%04x-%04x-%04x-%08x%04x}",
		hash,
		(hash >> 16) & 0xFFFF,
		(hash >> 8) & 0xFFFF,
		hash & 0xFFFF,
		hash ^ 0x12345678,
		hash & 0xFFFF);

	return string(guid);
}

// 轻量级目录扫描，查找DLL文件
void libaryVersionInfo::scanDirectoryForDlls(const string& dirPath, vector<string>& libNames, vector<string>& libPaths)
{
	// 使用Windows API扫描目录
	string searchPath = dirPath + "*.dll";
	WIN32_FIND_DATAA findData;
	HANDLE hFind = FindFirstFileA(searchPath.c_str(), &findData);

	if (hFind != INVALID_HANDLE_VALUE) {
		do {
			if (!(findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)) {
				string fileName = findData.cFileName;
				string fullPath = dirPath + fileName;

				// 移除.dll扩展名得到库名
				if (fileName.length() > 4 && fileName.substr(fileName.length() - 4) == ".dll") {
					string libName = fileName.substr(0, fileName.length() - 4);

					// 检查是否是目标库
					if (isTargetLibrary(libName, fullPath)) {
						bool exists = false;
						for (const auto& existingLib : libNames) {
							if (existingLib == libName) {
								exists = true;
								break;
							}
						}
						if (!exists) {
							libNames.push_back(libName);
							libPaths.push_back(fullPath);
						}
					}
				}
			}
		} while (FindNextFileA(hFind, &findData));

		FindClose(hFind);
	}
}
#endif

#endif