// 简单的编译测试
#include <iostream>
#include "libaryVersionInfo.h"

int main() {
    std::cout << "Testing libaryVersionInfo compilation..." << std::endl;
    
    // 测试基本实例化
    libaryVersionInfo* versionInfo = new libaryVersionInfo();
    
    if (versionInfo) {
        std::cout << "libaryVersionInfo instance created successfully" << std::endl;
        
        // 测试方法调用
        versionInfo->GetVersionInfoByOrder();
        versionInfo->PrintVersionInfo();
        
        delete versionInfo;
        std::cout << "Test completed successfully" << std::endl;
    }
    
    return 0;
}
